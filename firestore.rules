rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {

    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }

    function isOwner(uid) {
      return request.auth.uid == uid;
    }

    function getUserRole() {
      return get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role;
    }

    function isAdmin() {
      return isAuthenticated() && getUserRole() == 'admin';
    }

    function isVendor() {
      return isAuthenticated() && getUserRole() == 'vendor';
    }

    function isCustomer() {
      return isAuthenticated() && getUserRole() == 'customer';
    }

    function isActiveUser() {
      return get(/databases/$(database)/documents/users/$(request.auth.uid)).data.isActive == true;
    }

    // ===== USERS COLLECTION =====
    match /users/{userId} {
      // Users can read their own document, admins can read all
      allow read: if isOwner(userId) || isAdmin();

      // Users can create their own document during signup
      // Allow admin role creation for initial admin setup
      allow create: if isOwner(userId) &&
                       request.resource.data.uid == userId &&
                       (request.resource.data.email == request.auth.token.email ||
                        (request.resource.data.role == 'admin' &&
                         request.resource.data.email in ['<EMAIL>', '<EMAIL>']));

      // Users can update their own document (except role), admins can update any
      // Allow role updates for initial admin setup
      allow update: if (isOwner(userId) &&
                       (!('role' in request.resource.data.diff(resource.data).affectedKeys()) ||
                        (request.resource.data.role == 'admin' &&
                         request.resource.data.email in ['<EMAIL>', '<EMAIL>']))) ||
                       isAdmin();

      // Only admins can delete users
      allow delete: if isAdmin();
    }

    // ===== ADMINS COLLECTION =====
    match /admins/{adminId} {
      // Only admins can read admin documents
      allow read: if isAdmin();

      // Allow admin creation for:
      // 1. Super admins creating new admins
      // 2. Initial admin creation (when no admin documents exist yet)
      allow create: if (isAdmin() &&
                       get(/databases/$(database)/documents/admins/$(request.auth.uid)).data.accessLevel == 'super_admin') ||
                       (isOwner(adminId) &&
                        request.resource.data.accessLevel == 'super_admin' &&
                        !exists(/databases/$(database)/documents/admins/$(request.auth.uid)));

      // Admins can update their own document, super admins can update any
      allow update: if (isAdmin() && isOwner(adminId)) ||
                       (isAdmin() &&
                        get(/databases/$(database)/documents/admins/$(request.auth.uid)).data.accessLevel == 'super_admin');

      // Only super admins can delete admin documents
      allow delete: if isAdmin() &&
                       get(/databases/$(database)/documents/admins/$(request.auth.uid)).data.accessLevel == 'super_admin';

      // Admin Actions subcollection
      match /adminActions/{actionId} {
        allow read, write: if isAdmin() && isOwner(adminId);
      }
    }

    // ===== VENDORS COLLECTION =====
    match /vendors/{vendorId} {
      // Vendors can read their own document, admins can read all, customers can read approved vendors
      allow read: if isOwner(vendorId) ||
                     isAdmin() ||
                     (isCustomer() && resource.data.status == 'approved');

      // Only vendors can create their own document
      allow create: if isVendor() && isOwner(vendorId) && isActiveUser();

      // Vendors can update their own document (except status), admins can update any
      allow update: if (isVendor() && isOwner(vendorId) && isActiveUser() &&
                       !('status' in request.resource.data.diff(resource.data).affectedKeys())) ||
                       isAdmin();

      // Only admins can delete vendor documents
      allow delete: if isAdmin();

      // Vendor Documents subcollection
      match /vendorDocuments/{documentId} {
        allow read: if isOwner(vendorId) || isAdmin();
        allow create: if isVendor() && isOwner(vendorId) && isActiveUser();
        allow update: if isAdmin(); // Only admins can approve/reject documents
        allow delete: if isOwner(vendorId) || isAdmin();
      }

      // Vendor Analytics subcollection
      match /vendorAnalytics/{analyticsId} {
        allow read: if isOwner(vendorId) || isAdmin();
        allow write: if isAdmin(); // Only system/admins can write analytics
      }

      // Vendor Payouts subcollection
      match /vendorPayouts/{payoutId} {
        allow read: if isOwner(vendorId) || isAdmin();
        allow create: if isAdmin(); // Only admins can create payouts
        allow update: if isAdmin(); // Only admins can update payout status
      }
    }

    // ===== DRIVERS COLLECTION =====
    // Not implemented yet

    // ===== CUSTOMERS COLLECTION =====
    match /customers/{customerId} {
      // Customers can read their own document, admins can read all
      allow read: if isOwner(customerId) || isAdmin();

      // Only customers can create their own document
      allow create: if isCustomer() && isOwner(customerId) && isActiveUser();

      // Customers can update their own document, admins can update any
      allow update: if (isCustomer() && isOwner(customerId) && isActiveUser()) || isAdmin();

      // Only admins can delete customer documents
      allow delete: if isAdmin();

      // Customer Addresses subcollection
      match /customerAddresses/{addressId} {
        allow read, write: if isCustomer() && isOwner(customerId) && isActiveUser();
        allow read: if isAdmin();
      }

      // Customer Payment Methods subcollection
      match /customerPaymentMethods/{paymentMethodId} {
        allow read, write: if isCustomer() && isOwner(customerId) && isActiveUser();
        allow read: if isAdmin();
      }

      // Customer Preferences subcollection
      match /customerPreferences/{preferencesId} {
        allow read, write: if isCustomer() && isOwner(customerId) && isActiveUser();
      }
    }

    // ===== RESTAURANTS COLLECTION =====
    match /restaurants/{restaurantId} {
      // Everyone can read restaurants (public access for food delivery app)
      // Owners and admins can read all, others can read approved restaurants
      allow read: if true;

      // Only vendors can create restaurants
      allow create: if isVendor() && isActiveUser() &&
                       request.resource.data.ownerId == request.auth.uid;

      // Restaurant owners can update their own restaurants (except status), admins can update any
      allow update: if (isVendor() && isOwner(resource.data.ownerId) && isActiveUser() &&
                       !('status' in request.resource.data.diff(resource.data).affectedKeys())) ||
                       isAdmin();

      // Only admins can delete restaurants
      allow delete: if isAdmin();

      // Menu Items subcollection
      match /menuItems/{itemId} {
        allow read: if true; // Everyone can read menu items
        allow write: if isVendor() && isOwner(resource.data.restaurantOwnerId) && isActiveUser();
      }
    }

    // ===== ORDERS COLLECTION =====
    // Not implemented yet

    // ===== CATEGORIES COLLECTION =====
    match /categories/{categoryId} {
      // Everyone can read categories (public access for food delivery app)
      allow read: if true;
      // Only admins can write categories
      allow write: if isAdmin();
    }

    // ===== SYSTEM COLLECTION =====

    // System documentation - admins can read/write, others can read
    match /_system/{docId} {
      allow read: if true; // Allow developers to read documentation
      allow write: if isAdmin();
    }


  }
}
